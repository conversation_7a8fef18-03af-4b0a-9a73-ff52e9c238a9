# 绿盟漏洞扫描器 SOAR 组件

## 📋 组件概述

绿盟漏洞扫描器 SOAR 组件是一个专业的安全自动化组件，封装了绿盟漏洞扫描系统的完整 API 接口。该组件专门用于自动化查询和分析原理扫描漏洞，为安全运营团队提供高效的漏洞管理能力。

### 🎯 核心价值
- **自动化运营**: 无需人工干预，自动完成漏洞数据收集和分析
- **标准化输出**: 统一的数据格式，便于后续处理和集成
- **零配置部署**: 开箱即用，无需复杂配置
- **智能识别**: 自动识别验证码，提高登录成功率

## ✨ 功能特性

### 🔐 智能登录系统
- **自动登录**: 支持用户名密码自动登录
- **验证码识别**: 集成 ddddocr 库，自动识别验证码
- **重试机制**: 最多5次重试，确保登录成功
- **SSL处理**: 自动处理自签名证书问题

### 🔍 漏洞查询分析
- **原理漏洞过滤**: 专门提取原理扫描类型的漏洞
- **批量任务处理**: 自动查询所有已完成的扫描任务
- **分页数据获取**: 自动处理大量数据的分页查询
- **统计分析**: 提供详细的漏洞统计信息

### 📊 数据处理能力
- **格式化输出**: 标准化的 JSON 数据格式
- **多维度统计**: 按等级、主机、严重程度等维度统计
- **详细信息**: 包含漏洞描述、修复建议、CVE 编号等完整信息

## 🚀 支持的操作

### 查询所有原理漏洞 (get_all_principle_vulns)

获取绿盟系统中所有已完成任务的原理扫描漏洞。

#### 📥 输入参数
**无需传入任何参数** - 组件使用内置默认配置

#### ⚙️ 内置配置
- **主机地址**: `************`
- **登录凭据**: 使用默认的用户名和加密密码
- **扫描范围**: 所有状态为已完成(15)的任务
- **漏洞类型**: 仅提取包含"原理扫描"标识的漏洞

#### 📤 返回结果
```json
{
  "status": 0,
  "result": {
    "total_tasks": 4,
    "task_ids": [
      794,
      793,
      789,
      788
    ],
    "total_vulns": 2,
    "vulns": [
      {
        "vuln_name": "Ollama 未授权访问漏洞(CNVD-2025-04094)【原理扫描】",
        "vuln_level": "high",
        "host": "*************",
        "port": "未知端口",
        "protocol": "未知协议",
        "task_id": "",
        "vuln_id": 62814,
        "severity_points": 7.8,
        "description": "Ollama 是一个支持本地和云端运行的 AI 大模型推理工具。\nOllama 默认未设置权限访问，导致未授权的攻击者可以直接使用该模型进行对话。",
        "solution": "解决方案：\n1.配置身份验证\n2.限制IP访问控制",
        "cve_id": "",
        "cnvd": "CNVD-2025-04094",
        "cnnvd": "",
        "date_found": "2025-02-14",
        "threat_level": 2,
        "plugin_id": 62813,
        "scan_method": 3,
        "is_dangerous": false
      },
      {
        "vuln_name": "Ollama 敏感信息泄露漏洞【原理扫描】",
        "vuln_level": "high",
        "host": "*************",
        "port": "未知端口",
        "protocol": "未知协议",
        "task_id": "",
        "vuln_id": 62813,
        "severity_points": 7.8,
        "description": "Ollama 是一个支持本地和云端运行的 AI 大模型推理工具。\nOllama 默认未设置权限访问，导致敏感信息泄露。",
        "solution": "解决方案：\n1.配置身份验证\n2.限制IP访问控制\n",
        "cve_id": "",
        "cnvd": "",
        "cnnvd": "",
        "date_found": "2025-02-14",
        "threat_level": 2,
        "plugin_id": 62813,
        "scan_method": 3,
        "is_dangerous": false
      }
    ],
    "summary": {
      "total_count": 2,
      "level_count": {
        "high": 2
      },
      "target_count": {
        "*************": 2
      },
      "severity_distribution": {
        "high": 2,
        "medium": 0,
        "low": 0
      }
    },
    "host": "************"
  }
}
```

## 🔧 技术架构

### 核心组件
组件采用模块化设计，所有功能已集成到单一文件中：

- ✅ **GreenLeagueLogin**: 登录管理类，处理验证码识别和自动登录
- ✅ **TaskManager**: 任务管理类，负责查询和过滤扫描任务
- ✅ **VulnManager**: 漏洞管理类，处理漏洞数据获取和分析
- ✅ **自动重试机制**: 内置智能重试逻辑，提高操作成功率
- ✅ **SSL证书处理**: 自动处理HTTPS自签名证书问题

### 依赖要求

#### 必需依赖
```python
requests>=2.25.0      # HTTP请求库
urllib3>=1.26.0       # HTTP客户端库
loguru>=0.5.0         # 日志记录库
```

#### 可选依赖
```python
ddddocr>=1.4.0        # 验证码识别库（强烈推荐）
```

> **注意**: 如果未安装 `ddddocr`，组件将使用默认验证码值，可能影响登录成功率。

## 📖 使用指南

### 在 SOAR 工作流中使用

#### 步骤1: 添加组件
1. 在 SOAR 平台中打开工作流编辑器
2. 从组件库中拖拽"绿盟漏洞扫描器"组件到画布
3. 双击组件进行配置

#### 步骤2: 配置操作
1. 选择"查询所有原理漏洞"操作
2. **无需配置任何参数** - 组件使用内置默认配置
3. 可选：配置后续处理节点来处理返回的漏洞数据

#### 步骤3: 执行工作流
1. 保存工作流配置
2. 点击"运行"按钮执行工作流
3. 查看执行结果和漏洞数据

### 本地测试使用

```bash
# 进入组件目录
cd apps/nsfocus_vuln_scanner/main

# 直接运行测试
python run.py
```

### 数据处理示例

基于真实返回数据的处理示例：

```python
import json

# 假设这是从组件获取的返回数据
response = {
    "status": 0,
    "result": {
        "total_tasks": 4,
        "task_ids": [794, 793, 789, 788],
        "total_vulns": 2,
        "vulns": [...],  # 漏洞数据
        "summary": {...},  # 统计数据
        "host": "************"
    }
}

# 检查操作是否成功
if response["status"] == 0:
    result = response["result"]

    # 获取基本统计信息
    print(f"扫描主机: {result['host']}")
    print(f"已完成任务数: {result['total_tasks']}")
    print(f"发现原理漏洞数: {result['total_vulns']}")

    # 处理漏洞数据
    for vuln in result["vulns"]:
        print(f"\n漏洞名称: {vuln['vuln_name']}")
        print(f"影响主机: {vuln['host']}")
        print(f"危险等级: {vuln['vuln_level']}")
        print(f"CVSS评分: {vuln['severity_points']}")

        # 处理CNVD编号
        if vuln['cnvd']:
            print(f"CNVD编号: {vuln['cnvd']}")

        # 处理多行描述
        description = vuln['description'].replace('\\n', '\n')
        print(f"漏洞描述: {description}")

        # 处理修复建议
        solution = vuln['solution'].replace('\\n', '\n')
        print(f"修复建议: {solution}")

    # 统计分析
    summary = result["summary"]
    print(f"\n=== 统计分析 ===")
    print(f"漏洞等级分布: {summary['level_count']}")
    print(f"受影响主机: {summary['target_count']}")
    print(f"严重程度分布: {summary['severity_distribution']}")

else:
    print(f"操作失败: {response['result']}")
```

## 🔄 工作流程详解

### 阶段1: 系统登录 (约10-30秒)
1. **初始化连接**: 建立与绿盟系统的HTTPS连接
2. **获取验证码**: 从系统获取验证码图片和标识符
3. **识别验证码**: 使用OCR技术自动识别验证码
4. **执行登录**: 提交登录凭据完成身份验证
5. **获取Token**: 提取访问令牌用于后续API调用

### 阶段2: 任务查询 (约5-15秒)
1. **获取任务列表**: 查询系统中的所有扫描任务
2. **状态过滤**: 筛选出状态为"已完成"(15)的任务
3. **任务统计**: 统计已完成任务的数量和基本信息

### 阶段3: 漏洞分析 (约30-120秒，取决于数据量)
1. **批量查询**: 遍历所有已完成任务，获取漏洞数据
2. **分页处理**: 自动处理大量数据的分页查询
3. **原理过滤**: 筛选出包含"原理扫描"标识的漏洞
4. **数据整合**: 合并所有任务的漏洞数据

### 阶段4: 数据处理 (约5-10秒)
1. **格式化**: 将原始数据转换为标准格式
2. **统计分析**: 生成多维度的漏洞统计信息
3. **结果封装**: 封装最终结果并返回

## ⚠️ 注意事项

### 网络要求
- **连通性**: 确保 SOAR 系统能够访问绿盟漏洞扫描系统 (`************`)
- **端口**: 需要开放 HTTPS (443) 端口
- **防火墙**: 确保防火墙规则允许相关网络访问

### 系统要求
- **绿盟系统状态**: 确保目标绿盟系统正常运行
- **扫描任务**: 系统中需要有已完成的扫描任务
- **用户权限**: 确保登录用户具有查看任务和漏洞的权限

### 性能考虑
- **执行时间**: 大量数据查询可能需要2-5分钟
- **内存使用**: 大量漏洞数据可能占用较多内存
- **并发限制**: 避免同时运行多个实例访问同一系统

### 错误处理
- **登录失败**: 自动重试最多5次，每次间隔2秒
- **网络超时**: 自动处理网络连接超时问题
- **数据异常**: 详细的错误日志便于问题排查

## 📊 返回数据说明

### 状态码
- `status: 0` - 操作成功
- `status: 1` - 操作失败，详细错误信息在 result 字段中

### 📝 数据特点说明

#### 任务ID格式
- 任务ID为数字类型，如：794, 793, 789, 788
- 按时间倒序排列，数字越大表示任务越新

#### 漏洞等级映射
- `high` - 高危漏洞
- `medium` - 中危漏洞
- `low` - 低危漏洞

#### 端口和协议处理
- 当无法确定端口时，显示为 "未知端口"
- 当无法确定协议时，显示为 "未知协议"
- 这是绿盟系统的正常行为，不影响漏洞的有效性

#### 编号字段
- `cve_id`、`cnvd`、`cnnvd` 字段可能为空字符串
- 有值时表示该漏洞已被相应的漏洞库收录
- CNVD编号格式：CNVD-YYYY-NNNNN

#### 数值字段
- `threat_level`：威胁等级数值，数字越大威胁越高
- `plugin_id`：检测插件的唯一标识
- `scan_method`：扫描方法代码，3表示原理扫描
- `severity_points`：CVSS评分，范围0-10

### 漏洞字段说明
| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `vuln_name` | string | 漏洞名称，包含【原理扫描】标识 | "Ollama 未授权访问漏洞(CNVD-2025-04094)【原理扫描】" |
| `vuln_level` | string | 漏洞等级：high/medium/low | "high" |
| `host` | string | 受影响的主机IP地址 | "*************" |
| `port` | string | 受影响的端口号 | "未知端口" 或具体端口号 |
| `protocol` | string | 网络协议类型 | "未知协议" 或 "TCP/UDP" |
| `task_id` | string | 所属扫描任务ID | "" (可能为空) |
| `vuln_id` | number | 漏洞唯一标识符 | 62814 |
| `severity_points` | number | 漏洞严重程度评分(0-10) | 7.8 |
| `description` | string | 漏洞详细描述，支持换行符 | "Ollama 是一个支持本地和云端运行的 AI 大模型推理工具。\nOllama 默认未设置权限访问..." |
| `solution` | string | 修复建议和解决方案，支持换行符 | "解决方案：\n1.配置身份验证\n2.限制IP访问控制" |
| `cve_id` | string | CVE编号 | "" (可能为空) |
| `cnvd` | string | CNVD编号 | "CNVD-2025-04094" |
| `cnnvd` | string | CNNVD编号 | "" (可能为空) |
| `date_found` | string | 发现时间 (YYYY-MM-DD格式) | "2025-02-14" |
| `threat_level` | number | 威胁等级数值 | 2 |
| `plugin_id` | number | 检测插件ID | 62813 |
| `scan_method` | number | 扫描方法代码 | 3 |
| `is_dangerous` | boolean | 是否为危险漏洞 | false |

### 汇总统计字段说明
| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `total_tasks` | number | 已完成任务总数 | 4 |
| `task_ids` | array | 任务ID数组 | [794, 793, 789, 788] |
| `total_vulns` | number | 发现的原理漏洞总数 | 2 |
| `summary.total_count` | number | 漏洞总数 | 2 |
| `summary.level_count` | object | 按等级统计的漏洞数量 | {"high": 2} |
| `summary.target_count` | object | 按主机统计的漏洞数量 | {"*************": 2} |
| `summary.severity_distribution` | object | 按严重程度分布统计 | {"high": 2, "medium": 0, "low": 0} |
| `host` | string | 绿盟扫描器主机地址 | "************" |

## 📈 版本信息

- **当前版本**: v1.0
- **兼容性**: 绿盟漏扫查询漏洞 V3.0
- **更新日期**: 2025-07-18
- **维护状态**: 活跃维护中

## 🔗 相关链接

- **SOAR平台**: [W5 SOAR平台](https://w5.io)
- **技术文档**: [W5 帮助文档](https://w5.io/help/)
- **GitHub**: [W5 开源项目](https://github.com/w5teams/w5)
