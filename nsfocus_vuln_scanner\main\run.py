#!/usr/bin/env python
# encoding:utf-8
# cython: language_level=3

"""
绿盟漏洞扫描器SOAR组件
基于绿盟漏洞扫描系统API的W5 SOAR应用
提供3个主要功能：
1. 获取所有扫描任务
2. 获取指定任务的漏洞信息
3. 检查新完成的任务
"""

import requests
import json
import base64
import time
from datetime import datetime
from collections import defaultdict
from loguru import logger
from typing import Dict, Optional, Any


# 禁用SSL警告
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 尝试导入验证码识别库
try:
    import ddddocr
    HAS_DDDDOCR = True
except ImportError:
    HAS_DDDDOCR = False
    logger.warning("ddddocr模块未安装，验证码识别功能将不可用")



class GreenLeagueLogin:
    """绿盟登录管理类"""

    def __init__(self, host="************"):
        """
        初始化登录管理器
        参数:
            host: 服务器地址，默认为************
        """
        self.host = host
        self.base_url = f"https://{host}"
        self.session = requests.Session()
        self.token = None

        # 禁用SSL证书验证
        self.session.verify = False

        # 设置请求头，模拟浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'sec-ch-ua': '"Not_A Brand";v="99", "Google Chrome";v="109", "Chromium";v="109"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'https://{host}/'
        })

        # 初始化验证码识别器
        if HAS_DDDDOCR:
            self.ocr = ddddocr.DdddOcr()
        else:
            self.ocr = None

    def get_captcha(self):
        """
        获取验证码图片和identifier
        返回: (captcha_image_data, identifier)
        """
        try:
            url = f"{self.base_url}/interface/myauth/captcha/"
            response = self.session.get(url)

            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    # 获取base64编码的图片数据和identifier
                    image_base64 = data['data']['mg_str']['image']
                    identifier = data['data']['identifier']

                    # 解码base64图片
                    image_data = base64.b64decode(image_base64)

                    logger.info(f"成功获取验证码，identifier: {identifier}")
                    return image_data, identifier
                else:
                    logger.error(f"获取验证码失败: {data.get('message', '未知错误')}")
                    return None, None
            else:
                logger.error(f"请求验证码失败，状态码: {response.status_code}")
                return None, None

        except Exception as e:
            logger.error(f"获取验证码异常: {str(e)}")
            return None, None

    def recognize_captcha(self, image_data):
        """
        识别验证码
        参数: image_data - 图片二进制数据
        返回: 识别结果字符串
        """
        try:
            if self.ocr:
                result = self.ocr.classification(image_data)
                logger.info(f"验证码识别结果: {result}")
                return result
            else:
                # 简单的验证码识别方法（返回固定值或随机值）
                # 在实际环境中，这里应该实现更复杂的识别逻辑
                result = "1234"  # 默认验证码
                logger.warning(f"使用默认验证码: {result}")
                return result
        except Exception as e:
            logger.error(f"验证码识别失败: {str(e)}")
            return None

    def login(self, username, password, captcha_code, identifier):
        """
        执行登录
        参数:
            username: 用户名
            password: 密码（已加密）
            captcha_code: 验证码
            identifier: 验证码标识符
        返回: (success, result_data)
        """
        try:
            url = f"{self.base_url}/interface/myauth/login"

            # 构造登录数据
            login_data = {
                "username": username,
                "password": password,
                "captcha_code": captcha_code,
                "identifier": identifier
            }

            # 设置Content-Type
            headers = {
                'Content-Type': 'application/json;charset=UTF-8',
                'Origin': f'https://{self.host}'
            }

            response = self.session.post(url, json=login_data, headers=headers)

            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    logger.info(f"登录成功: {data.get('message', '登录成功')}")

                    # 提取重要信息
                    user_info = data.get('data', {})
                    self.token = user_info.get('token')
                    username = user_info.get('user_info', {}).get('username')
                    group_name = user_info.get('user_info', {}).get('group_name')

                    logger.info(f"用户名: {username}")
                    logger.info(f"用户组: {group_name}")
                    logger.info(f"Token: {self.token[:50]}..." if self.token else "Token: 未获取到")

                    # 更新session的token
                    if self.token:
                        self.session.headers.update({'token': self.token})

                    return True, data
                else:
                    logger.error(f"登录失败: {data.get('message', '未知错误')}")
                    return False, data
            else:
                logger.error(f"登录请求失败，状态码: {response.status_code}")
                return False, None

        except Exception as e:
            logger.error(f"登录异常: {str(e)}")
            return False, None

    def auto_login(self, username="admin", password="U2FsdGVkX18pUgexLHc5Y8RrbtAlXz+3EZrDpYJqFqE=", max_retries=5):
        """
        自动登录流程
        参数:
            username: 用户名，默认为admin
            password: 加密后的密码
            max_retries: 最大重试次数，默认为5
        返回: (success, result_data)
        """
        logger.info("开始绿盟自动登录流程")

        for attempt in range(max_retries):
            logger.info(f"第 {attempt + 1} 次尝试登录...")

            # 步骤1: 获取验证码
            logger.info("1. 获取验证码...")
            image_data, identifier = self.get_captcha()

            if not image_data or not identifier:
                logger.warning("获取验证码失败，重试...")
                time.sleep(1)
                continue

            # 步骤2: 识别验证码
            logger.info("2. 识别验证码...")
            captcha_code = self.recognize_captcha(image_data)

            if not captcha_code:
                logger.warning("验证码识别失败，重试...")
                time.sleep(1)
                continue

            # 步骤3: 执行登录
            logger.info("3. 执行登录...")
            success, result = self.login(username, password, captcha_code, identifier)

            if success:
                logger.info("登录成功！")
                return True, result
            else:
                logger.warning(f"登录失败，{max_retries - attempt - 1} 次重试机会剩余")
                time.sleep(2)

        logger.error("登录失败，已达到最大重试次数")
        return False, None

    def is_logged_in(self):
        """
        检查是否已登录
        返回: bool
        """
        return self.token is not None

    def get_session(self):
        """
        获取已认证的session对象
        返回: requests.Session
        """
        return self.session

    def get_token(self):
        """
        获取登录token
        返回: str
        """
        return self.token

    def get_base_url(self):
        """
        获取基础URL（所有API都使用HTTPS）
        返回: str
        """
        return self.base_url


class TaskManager:
    """任务管理类"""

    def __init__(self, session, base_url):
        """
        初始化任务管理器
        参数:
            session: 已登录的requests session
            base_url: 基础URL
        """
        self.session = session
        self.base_url = base_url

    def get_task_list(self, page=1, page_size=10000000):
        """
        获取任务列表
        参数:
            page: 页码，默认为1
            page_size: 每页大小，默认为10000000
        返回: (success, task_data)
        """
        try:
            url = f"{self.base_url}/interface/task/task_list/"

            request_data = {
                "page": page,
                "page_size": page_size
            }

            headers = {
                'Content-Type': 'application/json;charset=UTF-8'
            }

            response = self.session.post(url, json=request_data, headers=headers)

            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    results = data.get('data', {}).get('task_list', [])
                    paginator_data = data.get('data', {}).get('paginator_data', {})
                    total_count = paginator_data.get('total_records', 0)
                    logger.info(f"成功获取任务列表，当前页 {len(results)} 个任务，总计 {total_count} 个任务")
                    return True, data
                else:
                    logger.error(f"获取任务列表失败: {data.get('message', '未知错误')}")
                    return False, data
            else:
                logger.error(f"请求任务列表失败，状态码: {response.status_code}")
                return False, None

        except Exception as e:
            logger.error(f"获取任务列表异常: {str(e)}")
            return False, None

    def get_all_tasks(self):
        """
        获取所有扫描任务（自动分页）
        返回: (success, all_tasks_list)
        """
        all_tasks = []
        page = 1
        page_size = 10000000  # 使用大页面大小

        logger.info("正在获取所有扫描任务...")

        try:
            success, task_data = self.get_task_list(page=page, page_size=page_size)
            if not success:
                return False, []

            tasks = task_data.get('data', {}).get('task_list', [])
            all_tasks.extend(tasks)

            logger.info(f"成功获取所有扫描任务，共 {len(all_tasks)} 个")
            return True, all_tasks

        except Exception as e:
            logger.error(f"获取所有任务异常: {str(e)}")
            return False, []

    def get_task_vulns(self, task_id, page=1, size=0, filter_levels='high,middle,low'):
        """
        获取指定任务的漏洞分布信息
        参数:
            task_id: 任务ID
            page: 页码，默认为1
            size: 每页大小，默认为0（获取所有）
            filter_levels: 过滤漏洞等级，默认为'high,middle,low'
        返回: (success, vuln_data)
        """
        try:
            url = f"{self.base_url}/interface/report/sys/vuln-distribution/{task_id}"
            params = {
                'task_ids': task_id,
                'source': 'online',
                'page': page,
                'size': size,
                'filterVulnLevels': filter_levels,
                'vul_category_id': ''
            }

            response = self.session.get(url, params=params)

            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    vuln_info = data.get('data', {}).get('vulns_info', {})
                    vuln_list = vuln_info.get('vuln_distribution', {}).get('vuln_list', [])

                    vuln_level_count = vuln_info.get('vuln_distribution', {}).get('vuln_level_count', {})
                    hosts_count = vuln_info.get('vuln_distribution', {}).get('hosts_count', 0)

                    logger.info(f"成功获取任务 {task_id} 的漏洞信息，共 {len(vuln_list)} 个漏洞，影响 {hosts_count} 个主机")
                    logger.info(f"漏洞等级分布: {vuln_level_count}")

                    return True, data
                else:
                    logger.error(f"获取任务 {task_id} 漏洞信息失败: {data.get('message', '未知错误')}")
                    return False, data
            else:
                logger.error(f"请求任务 {task_id} 漏洞信息失败，状态码: {response.status_code}")
                return False, None

        except Exception as e:
            logger.error(f"获取任务 {task_id} 漏洞信息异常: {str(e)}")
            return False, None

    def get_completed_tasks(self):
        """
        获取已完成的任务（task_status = 15）
        只在第一页任务中查找
        返回: (success, completed_tasks)
        """
        logger.info("正在查询已完成的任务...")

        success, task_data = self.get_task_list(page=1, page_size=10)
        if not success:
            return False, []

        # 获取第一页的任务列表
        tasks = task_data.get('data', {}).get('task_list', [])

        # 过滤已完成的任务
        completed_tasks = [task for task in tasks if task.get('task_status') == 15]

        logger.info(f"在第一页中找到 {len(completed_tasks)} 个已完成的任务")

        return True, completed_tasks

    def get_child_task_list(self, parent_id, page=1, page_size=1000000):
        """
        获取子任务列表
        参数:
            parent_id: 父任务ID
            page: 页码，默认为1
            page_size: 每页大小，默认为1000000
        返回: (success, child_task_data)
        """
        try:
            url = f"{self.base_url}/interface/task/child_task_list/"

            # 构造请求数据
            request_data = {
                "parent_id": parent_id,
                "page_size": page_size,
                "page": page
            }

            # 设置请求头
            headers = {
                'Content-Type': 'application/json;charset=UTF-8'
            }

            response = self.session.post(url, json=request_data, headers=headers)

            logger.info(f"请求子任务列表 - 父任务ID: {parent_id}, 页码: {page}")

            if response.status_code == 200:
                data = response.json()

                if data.get('code') == 200:
                    child_tasks = data.get('data', {}).get('task_list', [])
                    paginator_data = data.get('data', {}).get('paginator_data', {})
                    total_child_tasks = paginator_data.get('total_child_tasks', 0)
                    logger.info(f"成功获取父任务 {parent_id} 的子任务列表，共 {len(child_tasks)} 个子任务，总计 {total_child_tasks} 个")
                    return True, data
                else:
                    logger.error(f"获取父任务 {parent_id} 的子任务列表失败: code={data.get('code')}, message={data.get('message', '未知错误')}")
                    return False, data
            else:
                logger.error(f"请求父任务 {parent_id} 的子任务列表失败，状态码: {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                return False, None

        except Exception as e:
            logger.error(f"获取父任务 {parent_id} 的子任务列表异常: {str(e)}")
            return False, None

    def get_all_tasks_with_subtasks(self):
        """
        获取所有任务及其子任务
        返回: (success, all_tasks_with_subtasks)
        """
        logger.info("正在获取所有任务及其子任务...")

        # 首先获取所有主任务
        success, main_tasks = self.get_all_tasks()
        if not success:
            return False, []

        all_tasks = []

        for task in main_tasks:
            # 添加主任务
            task_info = dict(task)
            task_info['is_subtask'] = False
            task_info['parent_task_id'] = task.get('task_parent_id')
            all_tasks.append(task_info)

            # 检查是否有子任务
            task_exp_info = task.get('tasks_exp_info', {})
            has_child = task_exp_info.get('task_exp_child', False)

            if has_child:
                task_id = task.get('task_id')
                logger.info(f"任务 {task_id} 有子任务，正在获取...")

                # 获取子任务
                success, child_data = self.get_child_task_list(task_id)
                if success:
                    child_tasks = child_data.get('data', {}).get('task_list', [])
                    for child_task in child_tasks:
                        child_info = dict(child_task)
                        child_info['is_subtask'] = True
                        child_info['parent_task_id'] = task_id
                        all_tasks.append(child_info)
                    logger.info(f"成功获取任务 {task_id} 的 {len(child_tasks)} 个子任务")
                else:
                    logger.warning(f"获取任务 {task_id} 的子任务失败")

        logger.info(f"总共获取到 {len(all_tasks)} 个任务（包括主任务和子任务）")
        return True, all_tasks


# 全局变量用于缓存登录状态
_login_manager = None
_task_manager = None
_last_login_time = None
LOGIN_CACHE_DURATION = 3600  # 登录缓存1小时

# 全局变量用于存储上次任务状态
_last_completed_tasks = set()  # 存储上次已完成任务的ID集合


def get_authenticated_managers():
    """
    获取已认证的管理器实例，如果需要则重新登录
    返回: (login_manager, task_manager, success)
    """
    global _login_manager, _task_manager, _last_login_time

    current_time = time.time()

    # 检查是否需要重新登录
    if (_login_manager is None or _task_manager is None or
        _last_login_time is None or
        current_time - _last_login_time > LOGIN_CACHE_DURATION):

        logger.info("需要重新登录绿盟系统")

        # 创建登录管理器
        login_manager = GreenLeagueLogin(host="************")
        success, _ = login_manager.auto_login()

        if not success:
            logger.error("登录失败，无法继续后续操作")
            return None, None, False

        # 创建任务管理器
        session = login_manager.get_session()
        base_url = login_manager.get_base_url()
        task_manager = TaskManager(session, base_url)

        # 更新全局缓存
        _login_manager = login_manager
        _task_manager = task_manager
        _last_login_time = current_time

        logger.info("登录成功，管理器已缓存")

    return _login_manager, _task_manager, True


def _get_task_type_name(task_type: int) -> str:
    """将任务类型数字转换为中文名称"""
    task_type_map = {
        1: "评估任务",
        8: "Web扫描"
    }
    return task_type_map.get(task_type, f"未知类型({task_type})")


def _get_task_status_name(task_status: int) -> str:
    """将任务状态数字转换为中文名称"""
    status_map = {
        3: "等待中",
        9: "运行中",
        15: "已完成",
        # 可以根据实际情况添加更多状态
    }
    return status_map.get(task_status, f"状态{task_status}")


def _translate_risk_level(risk_level: str) -> str:
    """将风险等级英文转换为中文"""
    risk_level_map = {
        "high": "非常危险",
        "middle": "比较危险",
        "unknown": "未知风险",
        "safe": "非常安全"
    }
    return risk_level_map.get(risk_level, risk_level)


def _extract_task_fields(task: Dict[str, Any]) -> Dict[str, Any]:
    """提取任务的指定字段"""
    vuln_count = task.get('scan_vuln_count') or {}

    return {
        "任务号": task.get('task_id'),
        "任务名称": task.get('task_name'),
        "任务类型": _get_task_type_name(task.get('task_type', 0)),
        "任务状态": _get_task_status_name(task.get('task_status', 0)),
        "任务整体风险等级": _translate_risk_level(task.get('risk_task_point', 'unknown')),
        "漏洞统计": {
            "高危": vuln_count.get('high_count', 0) if vuln_count else 0,
            "中危": vuln_count.get('medium_count', 0) if vuln_count else 0,
            "低危": vuln_count.get('low_count', 0) if vuln_count else 0,
            "总数": vuln_count.get('total_count', 0) if vuln_count else 0
        },
        "任务开始执行时间": task.get('task_start_scan'),
        "任务结束执行时间": task.get('time_end_scan')
    }


def _extract_vulnerability_fields(vuln: Dict[str, Any]) -> Dict[str, Any]:
    """提取漏洞的指定字段"""
    # 提取CVE和CNVD编号
    cve_cnvd = []
    if vuln.get('cve_id'):
        cve_cnvd.append(vuln['cve_id'])
    if vuln.get('cnvd'):
        cve_cnvd.append(vuln['cnvd'])

    # 处理描述信息
    description = vuln.get('i18n_description', [])
    if isinstance(description, list):
        description = '\n'.join(description)

    # 处理解决方法
    solution = vuln.get('i18n_solution', [])
    if isinstance(solution, list):
        solution = '\n'.join(solution)

    return {
        "漏洞名称": vuln.get('i18n_name', ''),
        "CVE和CNVD编号": ', '.join(cve_cnvd) if cve_cnvd else '无',
        "漏洞等级": _translate_risk_level(vuln.get('vuln_level', 'unknown')),
        "受影响主机": vuln.get('target', ''),
        "详细描述": description,
        "解决方法": solution,
        "severity_points": vuln.get('severity_points', 0)  # 保留severity_points用于排序
    }


async def get_all_scan_tasks():
    """获取绿盟漏洞扫描系统中的所有扫描任务。

    Returns:
        Dict[str, Any]: 包含所有扫描任务信息的字典
            - status: 0 表示成功，1 表示失败
            - result: 任务列表数据（成功时）或错误信息（失败时）
    """
    logger.info("[绿盟漏扫] 开始获取所有扫描任务")

    try:
        # 获取已认证的管理器
        _, task_manager, success = get_authenticated_managers()

        if not success:
            return {
                "status": 1,
                "result": "登录绿盟系统失败"
            }

        # 获取所有任务
        success, all_tasks = task_manager.get_all_tasks()

        if not success:
            return {
                "status": 1,
                "result": "获取所有任务失败"
            }

        # 提取指定字段
        simplified_tasks = [_extract_task_fields(task) for task in all_tasks]

        logger.info(f"成功获取 {len(simplified_tasks)} 个扫描任务")

        return {
            "status": 0,
            "result": {
                "data": simplified_tasks,
                "total_tasks": len(simplified_tasks),
                "message": f"成功获取 {len(simplified_tasks)} 个扫描任务"
            }
        }

    except Exception as e:
        logger.error(f"获取所有任务出错: {str(e)}")
        return {
            "status": 1,
            "result": f"获取所有任务出错: {str(e)}"
        }


async def get_task_vulnerabilities(task_id):
    """获取指定任务的所有漏洞信息。

    Args:
        task_id: 任务ID

    Returns:
        Dict[str, Any]: 包含漏洞信息的字典
            - status: 0 表示成功，1 表示失败
            - result: 漏洞列表数据（成功时）或错误信息（失败时）
    """
    logger.info(f"[绿盟漏扫] 开始获取任务 {task_id} 的漏洞信息")

    try:
        # 验证参数
        if not task_id or not task_id.strip():
            return {
                "status": 1,
                "result": "任务ID不能为空"
            }

        # 获取已认证的管理器
        _, task_manager, success = get_authenticated_managers()

        if not success:
            return {
                "status": 1,
                "result": "登录绿盟系统失败"
            }

        # 获取漏洞信息
        success, vuln_data = task_manager.get_task_vulns(task_id, filter_levels="high,middle,low")

        if not success:
            return {
                "status": 1,
                "result": f"获取任务 {task_id} 的漏洞信息失败"
            }

        # 解析漏洞数据
        vuln_info = vuln_data.get('data', {}).get('vulns_info', {})
        vuln_distribution = vuln_info.get('vuln_distribution', {})
        vuln_list = vuln_distribution.get('vuln_list', [])
        vuln_level_count = vuln_distribution.get('vuln_level_count', {})
        hosts_count = vuln_distribution.get('hosts_count', 0)

        # 提取指定字段
        simplified_vulns = [_extract_vulnerability_fields(vuln) for vuln in vuln_list]

        # 根据漏洞名称排序：带有【原理扫描】的放前面，不带的放后面
        # 二级排序：在同一优先级内，按severity_points降序排列（大的在前）
        def sort_vulns_by_name(vuln):
            vuln_name = vuln.get("漏洞名称", "")
            severity_points = vuln.get("severity_points", 0)
            # 如果漏洞名称包含【原理扫描】，返回0（优先级高）
            # 否则返回1（优先级低）
            # 二级排序使用负数，使得severity_points大的排在前面
            if "【原理扫描】" in vuln_name:
                return (0, -severity_points)  # 0表示优先级高，-severity_points实现降序
            else:
                return (1, -severity_points)  # 1表示优先级低，-severity_points实现降序

        simplified_vulns.sort(key=sort_vulns_by_name)

        # 排序完成后，移除severity_points字段，不作为返回结果
        for vuln in simplified_vulns:
            vuln.pop('severity_points', None)

        # 简化漏洞等级统计
        simplified_vuln_stats = {
            "高危": vuln_level_count.get('high', 0),
            "中危": vuln_level_count.get('middle', 0),
            "低危": vuln_level_count.get('low', 0),
            "总数": vuln_level_count.get('vulns_count', len(vuln_list))
        }

        logger.info(f"成功获取任务 {task_id} 的漏洞信息，共 {len(simplified_vulns)} 个漏洞")

        return {
            "status": 0,
            "result": {
                "data": simplified_vulns,
                "task_id": task_id,
                "total_vulns": len(simplified_vulns),
                "漏洞统计": simplified_vuln_stats,
                "受影响主机数量": hosts_count,
                "message": f"成功获取任务 {task_id} 的 {len(simplified_vulns)} 个漏洞信息"
            }
        }

    except Exception as e:
        logger.error(f"获取任务漏洞出错: {str(e)}")
        return {
            "status": 1,
            "result": f"获取任务漏洞出错: {str(e)}"
        }


async def check_new_completed_tasks():
    """检查新完成的任务

    Returns:
        Dict[str, Any]: 包含新完成任务信息的字典
            - status: 0 表示成功，1 表示失败
            - result: 新完成任务的信息
    """
    global _last_completed_tasks

    logger.info("[绿盟漏扫] 开始检查新完成的任务")

    try:
        # 获取已认证的管理器
        _, task_manager, success = get_authenticated_managers()

        if not success:
            return {
                "status": 1,
                "result": "登录绿盟系统失败"
            }

        # 获取所有任务及其子任务
        logger.info("获取所有任务及其子任务...")
        success, all_tasks = task_manager.get_all_tasks_with_subtasks()

        if not success:
            return {
                "status": 1,
                "result": "获取任务列表失败"
            }

        # 筛选出已完成的任务（task_status = 15）
        current_completed_tasks = []
        current_completed_task_ids = set()

        for task in all_tasks:
            if task.get('task_status') == 15:  # 已完成状态
                current_completed_tasks.append(task)
                current_completed_task_ids.add(task.get('task_id'))

        logger.info(f"当前已完成任务数量: {len(current_completed_tasks)}")
        logger.info(f"上次已完成任务数量: {len(_last_completed_tasks)}")

        # 找出新完成的任务
        new_completed_task_ids = current_completed_task_ids - _last_completed_tasks
        new_completed_tasks = [task for task in current_completed_tasks
                              if task.get('task_id') in new_completed_task_ids]

        logger.info(f"新完成的任务数量: {len(new_completed_tasks)}")

        # 格式化新完成的任务信息
        formatted_new_tasks = []
        for task in new_completed_tasks:
            task_info = {
                "任务ID": task.get('task_id'),
                "任务名称": task.get('task_name'),
                "任务类型": _get_task_type_name(task.get('task_type', 0)),
                "是否为子任务": task.get('is_subtask', False),
                "父任务ID": task.get('parent_task_id') if task.get('is_subtask') else None,
                "任务创建时间": task.get('task_create_time'),
                "任务开始时间": task.get('task_begin_time'),
                "扫描结束时间": task.get('time_end_scan'),
                "风险等级": _translate_risk_level(task.get('risk_task_point', 'unknown')),
                "漏洞统计": task.get('scan_vuln_count', {}),
                "执行时间": task.get('execution_time', ''),
            }
            formatted_new_tasks.append(task_info)

        # 更新全局缓存
        _last_completed_tasks = current_completed_task_ids.copy()

        logger.info(f"成功检查新完成任务，发现 {len(new_completed_tasks)} 个新完成的任务")

        return {
            "status": 0,
            "result": {
                "total_tasks": len(new_completed_tasks),
                "new_completed_tasks": formatted_new_tasks,
                "current_total_completed": len(current_completed_tasks),
                "previous_total_completed": len(_last_completed_tasks) - len(new_completed_tasks),
                "host": "************",
                "check_time": datetime.now().isoformat(),
                "message": f"发现 {len(new_completed_tasks)} 个新完成的任务"
            }
        }

    except Exception as e:
        logger.error(f"检查新完成任务出错: {str(e)}")
        return {
            "status": 1,
            "result": f"检查新完成任务出错: {str(e)}"
        }






if __name__ == "__main__":
    """本地测试入口"""
    import asyncio

    # 配置日志
    logger.add("nsfocus_vuln_scanner.log", rotation="10 MB", level="INFO")

    async def test_main():
        """测试主函数"""
        logger.info("开始本地测试绿盟漏洞扫描器")

        try:
            # 测试获取所有扫描任务
            print("\n" + "="*50)
            print("测试1: 获取所有扫描任务")
            print("="*50)

            result1 = await get_all_scan_tasks()
            print(f"结果: {json.dumps(result1, indent=2, ensure_ascii=False)}")

            # 测试获取任务漏洞信息（需要一个有效的task_id）
            if result1.get("status") == 0 and result1.get("result", {}).get("data"):
                tasks = result1["result"]["data"]
                if tasks:
                    task_id = tasks[0].get("任务ID")
                    if task_id:
                        print("\n" + "="*50)
                        print(f"测试2: 获取任务 {task_id} 的漏洞信息")
                        print("="*50)

                        result2 = await get_task_vulnerabilities(task_id)
                        print(f"结果: {json.dumps(result2, indent=2, ensure_ascii=False)}")

            # 测试检查新完成任务
            print("\n" + "="*50)
            print("测试3: 检查新完成任务")
            print("="*50)

            result3 = await check_new_completed_tasks()
            print(f"结果: {json.dumps(result3, indent=2, ensure_ascii=False)}")

            print("\n" + "="*50)
            print("测试完成")
            print("="*50)

        except Exception as e:
            logger.error(f"本地测试异常: {str(e)}")
            print(f"❌ 测试异常: {str(e)}")

    # 运行测试
    asyncio.run(test_main())
