#!/usr/bin/env python
# encoding:utf-8
# cython: language_level=3

"""
绿盟漏洞扫描器SOAR组件
封装绿盟漏洞扫描系统API，查询所有原理漏洞
集成所有必要的类，无需外部依赖
"""

import requests
import json
import base64
import time
from datetime import datetime
from collections import defaultdict
from loguru import logger
from io import BytesIO

# 禁用SSL警告
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
import ddddocr
HAS_DDDDOCR = True



class GreenLeagueLogin:
    """绿盟登录管理类"""

    def __init__(self, host="************"):
        """
        初始化登录管理器
        参数:
            host: 服务器地址，默认为************
        """
        self.host = host
        self.base_url = f"https://{host}"  # 所有API都使用HTTPS
        self.session = requests.Session()
        self.token = None

        # 禁用SSL证书验证（因为服务器使用自签名证书）
        self.session.verify = False

        # 设置请求头，模拟浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'sec-ch-ua': '"Not_A Brand";v="99", "Google Chrome";v="109", "Chromium";v="109"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'https://{host}/'
        })

        # 初始化验证码识别器
        if HAS_DDDDOCR:
            self.ocr = ddddocr.DdddOcr()
        else:
            self.ocr = None

    def get_captcha(self):
        """
        获取验证码图片和identifier
        返回: (captcha_image_data, identifier)
        """
        try:
            url = f"{self.base_url}/interface/myauth/captcha/"
            response = self.session.get(url)

            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    # 获取base64编码的图片数据和identifier
                    image_base64 = data['data']['mg_str']['image']
                    identifier = data['data']['identifier']

                    # 解码base64图片
                    image_data = base64.b64decode(image_base64)

                    logger.info(f"成功获取验证码，identifier: {identifier}")
                    return image_data, identifier
                else:
                    logger.error(f"获取验证码失败: {data.get('message', '未知错误')}")
                    return None, None
            else:
                logger.error(f"请求验证码失败，状态码: {response.status_code}")
                return None, None

        except Exception as e:
            logger.error(f"获取验证码异常: {str(e)}")
            return None, None

    def recognize_captcha(self, image_data):
        """
        识别验证码
        参数: image_data - 图片二进制数据
        返回: 识别结果字符串
        """
        try:
            if self.ocr:
                result = self.ocr.classification(image_data)
                logger.info(f"验证码识别结果: {result}")
                return result
            else:
                # 简单的验证码识别方法（返回固定值或随机值）
                # 在实际环境中，这里应该实现更复杂的识别逻辑
                result = "1234"  # 默认验证码
                logger.warning(f"使用默认验证码: {result}")
                return result
        except Exception as e:
            logger.error(f"验证码识别失败: {str(e)}")
            return None

    def login(self, username, password, captcha_code, identifier):
        """
        执行登录
        参数:
            username: 用户名
            password: 密码（已加密）
            captcha_code: 验证码
            identifier: 验证码标识符
        返回: (success, result_data)
        """
        try:
            url = f"{self.base_url}/interface/myauth/login"

            # 构造登录数据
            login_data = {
                "username": username,
                "password": password,
                "captcha_code": captcha_code,
                "identifier": identifier
            }

            # 设置Content-Type
            headers = {
                'Content-Type': 'application/json;charset=UTF-8',
                'Origin': f'https://{self.host}'
            }

            response = self.session.post(url, json=login_data, headers=headers)

            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    logger.info(f"登录成功: {data.get('message', '登录成功')}")

                    # 提取重要信息
                    user_info = data.get('data', {})
                    self.token = user_info.get('token')
                    username = user_info.get('user_info', {}).get('username')
                    group_name = user_info.get('user_info', {}).get('group_name')

                    logger.info(f"用户名: {username}")
                    logger.info(f"用户组: {group_name}")
                    logger.info(f"Token: {self.token[:50]}..." if self.token else "Token: 未获取到")

                    # 更新session的token
                    if self.token:
                        self.session.headers.update({'token': self.token})

                    return True, data
                else:
                    logger.error(f"登录失败: {data.get('message', '未知错误')}")
                    return False, data
            else:
                logger.error(f"登录请求失败，状态码: {response.status_code}")
                return False, None

        except Exception as e:
            logger.error(f"登录异常: {str(e)}")
            return False, None

    def auto_login(self, username="admin", password="U2FsdGVkX18pUgexLHc5Y8RrbtAlXz+3EZrDpYJqFqE=", max_retries=5):
        """
        自动登录流程
        参数:
            username: 用户名，默认为admin
            password: 加密后的密码
            max_retries: 最大重试次数，默认为5
        返回: (success, result_data)
        """
        logger.info("开始绿盟自动登录流程")

        for attempt in range(max_retries):
            logger.info(f"第 {attempt + 1} 次尝试登录...")

            # 步骤1: 获取验证码
            logger.info("1. 获取验证码...")
            image_data, identifier = self.get_captcha()

            if not image_data or not identifier:
                logger.warning("获取验证码失败，重试...")
                time.sleep(1)
                continue

            # 步骤2: 识别验证码
            logger.info("2. 识别验证码...")
            captcha_code = self.recognize_captcha(image_data)

            if not captcha_code:
                logger.warning("验证码识别失败，重试...")
                time.sleep(1)
                continue

            # 步骤3: 执行登录
            logger.info("3. 执行登录...")
            success, result = self.login(username, password, captcha_code, identifier)

            if success:
                logger.info("登录成功！")
                return True, result
            else:
                logger.warning(f"登录失败，{max_retries - attempt - 1} 次重试机会剩余")
                time.sleep(2)

        logger.error("登录失败，已达到最大重试次数")
        return False, None

    def is_logged_in(self):
        """
        检查是否已登录
        返回: bool
        """
        return self.token is not None

    def get_session(self):
        """
        获取已认证的session对象
        返回: requests.Session
        """
        return self.session

    def get_token(self):
        """
        获取登录token
        返回: str
        """
        return self.token

    def get_base_url(self):
        """
        获取基础URL（所有API都使用HTTPS）
        返回: str
        """
        return self.base_url


class TaskManager:
    """绿盟任务管理类"""

    def __init__(self, session, base_url):
        """
        初始化任务管理器
        参数:
            session: 已认证的requests.Session对象
            base_url: 基础URL
        """
        self.session = session
        self.base_url = base_url

    def get_task_list(self, page=1, page_size=10):
        """
        获取任务列表
        参数:
            page: 页码，默认为1
            page_size: 每页大小，默认为10
        返回: (success, task_list_data)
        """
        try:
            url = f"{self.base_url}/interface/task/task_list/"

            # 构造请求数据
            request_data = {
                "page": page,
                "page_size": page_size
            }

            # 设置请求头
            headers = {
                'Content-Type': 'application/json;charset=UTF-8'
            }

            response = self.session.post(url, json=request_data, headers=headers)

            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    # 修正数据结构：实际返回的是 task_list 而不是 results
                    results = data.get('data', {}).get('task_list', [])
                    paginator_data = data.get('data', {}).get('paginator_data', {})
                    total_count = paginator_data.get('total_records', 0)
                    logger.info(f"成功获取任务列表，当前页 {len(results)} 个任务，总计 {total_count} 个任务")
                    return True, data
                else:
                    logger.error(f"获取任务列表失败: {data.get('message', '未知错误')}")
                    return False, data
            else:
                logger.error(f"请求任务列表失败，状态码: {response.status_code}")
                return False, None

        except Exception as e:
            logger.error(f"获取任务列表异常: {str(e)}")
            return False, None

    def get_completed_tasks(self):
        """
        获取已完成的任务（task_status = 15）
        只在第一页任务中查找
        返回: (success, completed_tasks)
        """
        logger.info("正在查询已完成的任务...")

        success, task_data = self.get_task_list(page=1, page_size=10)
        if not success:
            return False, []

        # 获取第一页的任务列表
        tasks = task_data.get('data', {}).get('task_list', [])

        # 过滤已完成的任务
        completed_tasks = [task for task in tasks if task.get('task_status') == 15]

        logger.info(f"在第一页中找到 {len(completed_tasks)} 个已完成的任务")

        return True, completed_tasks


class VulnManager:
    """绿盟漏洞管理类"""

    def __init__(self, session, base_url):
        """
        初始化漏洞管理器
        参数:
            session: 已认证的requests.Session对象
            base_url: 基础URL
        """
        self.session = session
        self.base_url = base_url

    def get_vuln_distribution(self, task_id, page=1, size=100):
        """
        获取指定任务的漏洞分布信息
        参数:
            task_id: 任务ID
            page: 页码，默认为1
            size: 每页大小，默认为100
        返回: (success, vuln_data)
        """
        try:
            # 构造URL，包含查询参数
            url = f"{self.base_url}/interface/report/sys/vuln-distribution/{task_id}"
            params = {
                'task_ids': task_id,
                'source': 'online',
                'page': page,
                'size': size,
                'filterVulnLevels': 'high,middle,low',
                'vul_category_id': ''
            }

            response = self.session.get(url, params=params)

            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    vuln_info = data.get('data', {}).get('vulns_info', {})
                    vuln_list = vuln_info.get('vuln_distribution', {}).get('vuln_list', [])
                    logger.info(f"成功获取任务 {task_id} 的漏洞信息，共 {len(vuln_list)} 个漏洞")
                    return True, data
                else:
                    logger.error(f"获取任务 {task_id} 漏洞信息失败: {data.get('message', '未知错误')}")
                    return False, data
            else:
                logger.error(f"请求任务 {task_id} 漏洞信息失败，状态码: {response.status_code}")
                return False, None

        except Exception as e:
            logger.error(f"获取任务 {task_id} 漏洞信息异常: {str(e)}")
            return False, None

    def get_all_vulns_for_task(self, task_id):
        """
        获取指定任务的所有漏洞（自动分页）
        参数:
            task_id: 任务ID
        返回: (success, all_vulns_list)
        """
        all_vulns = []
        page = 1
        size = 100

        logger.info(f"正在获取任务 {task_id} 的所有漏洞...")

        while True:
            success, vuln_data = self.get_vuln_distribution(task_id, page=page, size=size)
            if not success:
                return False, []

            vuln_info = vuln_data.get('data', {}).get('vulns_info', {})
            vuln_list = vuln_info.get('vuln_distribution', {}).get('vuln_list', [])

            if not vuln_list:
                break

            all_vulns.extend(vuln_list)

            # 检查是否还有更多页
            # 从响应中获取总数，如果没有更多数据则退出
            total_from_response = vuln_data.get('data', {}).get('total', 0)
            if len(vuln_list) < size or len(all_vulns) >= total_from_response:
                break

            page += 1

        logger.info(f"成功获取任务 {task_id} 的所有漏洞，共 {len(all_vulns)} 个")
        return True, all_vulns

    def filter_principle_scan_vulns(self, vuln_list):
        """
        过滤出原理扫描的漏洞
        参数:
            vuln_list: 漏洞列表
        返回: 原理扫描漏洞列表
        """
        principle_vulns = []

        for vuln in vuln_list:
            vuln_name = vuln.get('i18n_name', '')
            # 检查漏洞名称是否包含"原理扫描"标识
            if '原理扫描' in vuln_name or '【原理扫描】' in vuln_name:
                principle_vulns.append(vuln)

        return principle_vulns

    def get_principle_scan_vulns_for_task(self, task_id):
        """
        获取指定任务的原理扫描漏洞
        参数:
            task_id: 任务ID
        返回: (success, principle_vulns)
        """
        success, all_vulns = self.get_all_vulns_for_task(task_id)
        if not success:
            return False, []

        principle_vulns = self.filter_principle_scan_vulns(all_vulns)
        logger.info(f"任务 {task_id} 中发现 {len(principle_vulns)} 个原理扫描漏洞")

        return True, principle_vulns

    def get_vuln_statistics(self, vulns):
        """
        获取漏洞统计信息
        参数:
            vulns: 漏洞列表
        返回: dict 包含统计信息
        """
        if not vulns:
            return {}

        statistics = {
            'total_count': len(vulns),
            'level_count': defaultdict(int),
            'target_count': defaultdict(int),
            'severity_distribution': {
                'high': 0,      # >= 7.0
                'medium': 0,    # 4.0 - 6.9
                'low': 0        # < 4.0
            }
        }

        for vuln in vulns:
            # 统计风险等级
            level = vuln.get('vuln_level', '未知')
            statistics['level_count'][level] += 1

            # 统计目标
            target = vuln.get('target', '未知目标')
            statistics['target_count'][target] += 1

            # 统计严重程度分布
            severity = vuln.get('severity_points', 0)
            try:
                severity_float = float(severity)
                if severity_float >= 7.0:
                    statistics['severity_distribution']['high'] += 1
                elif severity_float >= 4.0:
                    statistics['severity_distribution']['medium'] += 1
                else:
                    statistics['severity_distribution']['low'] += 1
            except (ValueError, TypeError):
                statistics['severity_distribution']['low'] += 1

        return statistics

    def analyze_multiple_tasks_vulns(self, task_ids, focus_on_principle=True):
        """
        分析多个任务的漏洞信息
        参数:
            task_ids: 任务ID列表
            focus_on_principle: 是否只关注原理扫描漏洞
        返回: dict 包含分析结果
        """
        logger.info(f"开始分析 {len(task_ids)} 个任务的漏洞信息...")

        all_vulns = []
        task_results = {}

        for task_id in task_ids:
            logger.info(f"正在分析任务 {task_id}...")

            if focus_on_principle:
                success, vulns = self.get_principle_scan_vulns_for_task(task_id)
            else:
                success, vulns = self.get_all_vulns_for_task(task_id)

            if success:
                task_results[task_id] = vulns
                all_vulns.extend(vulns)
                logger.info(f"任务 {task_id}: 发现 {len(vulns)} 个漏洞")
            else:
                task_results[task_id] = []
                logger.info(f"任务 {task_id}: 获取漏洞信息失败")

        # 汇总统计
        logger.info(f"汇总统计: 总计发现漏洞 {len(all_vulns)} 个")

        return {
            'all_vulns': all_vulns,
            'task_results': task_results,
            'summary': self.get_vuln_statistics(all_vulns)
        }


async def get_all_principle_vulns():
    """获取所有已完成任务的原理漏洞 - 使用默认参数"""
    logger.info("[绿盟漏扫] 开始获取所有原理漏洞")

    try:
        # 使用example.py中的默认参数
        host = "************"

        # 步骤1: 登录系统
        logger.info("步骤1: 登录绿盟系统")
        login_manager = GreenLeagueLogin(host=host)
        success, login_result = login_manager.auto_login()

        if not success:
            logger.error("登录失败，无法继续后续操作")
            return {
                "status": 1,
                "result": f"登录失败: {login_result}"
            }

        # 步骤2: 初始化任务和漏洞管理器
        logger.info("步骤2: 初始化管理器")
        session = login_manager.get_session()
        base_url = login_manager.get_base_url()

        task_manager = TaskManager(session, base_url)
        vuln_manager = VulnManager(session, base_url)

        # 步骤3: 获取已完成的任务
        logger.info("步骤3: 获取已完成的任务")
        success, completed_tasks = task_manager.get_completed_tasks()

        if not success or not completed_tasks:
            logger.warning("没有找到已完成的任务")
            return {
                "status": 0,
                "result": {
                    'total_tasks': 0,
                    'total_vulns': 0,
                    'vulns': [],
                    'message': '没有找到已完成的任务',
                    'host': host
                }
            }

        # 步骤4: 分析已完成任务的原理扫描漏洞
        logger.info("步骤4: 分析原理扫描漏洞")
        task_ids = [task['task_id'] for task in completed_tasks]

        analysis_result = vuln_manager.analyze_multiple_tasks_vulns(
            task_ids,
            focus_on_principle=True
        )

        all_principle_vulns = analysis_result.get('all_vulns', [])

        # 格式化漏洞数据
        formatted_vulns = []
        for vuln in all_principle_vulns:
            # 处理描述信息 - 从i18n_description数组中提取
            description_list = vuln.get('i18n_description', [])
            description = '\n'.join(description_list) if description_list else vuln.get('description', '')

            # 处理解决方案 - 从i18n_solution数组中提取
            solution_list = vuln.get('i18n_solution', [])
            solution = '\n'.join(solution_list) if solution_list else vuln.get('solution', '')

            # 提取端口信息 - 可能在不同字段中
            port = vuln.get('port', '')
            if not port:
                # 尝试从其他可能的字段获取端口信息
                port = vuln.get('service_port', '')
            if not port:
                port = '未知端口'

            # 提取协议信息
            protocol = vuln.get('protocol', '')
            if not protocol:
                protocol = vuln.get('service_protocol', '')
            if not protocol:
                protocol = '未知协议'

            formatted_vuln = {
                'vuln_name': vuln.get('i18n_name', '未知漏洞'),
                'vuln_level': vuln.get('vuln_level', '未知'),
                'host': vuln.get('target', '未知主机'),
                'port': port,
                'protocol': protocol,
                'task_id': vuln.get('task_id', ''),
                'vuln_id': vuln.get('vul_id', vuln.get('vuln_id', '')),  # 尝试两个字段
                'severity_points': vuln.get('severity_points', 0),
                'description': description,
                'solution': solution,
                # 添加更多有用的字段
                'cve_id': vuln.get('cve_id', ''),
                'cnvd': vuln.get('cnvd', ''),
                'cnnvd': vuln.get('cnnvd', ''),
                'date_found': vuln.get('date_found', ''),
                'threat_level': vuln.get('threat_level', ''),
                'plugin_id': vuln.get('plugin_id', ''),
                'scan_method': vuln.get('scan_method', ''),
                'is_dangerous': vuln.get('is_dangerous', False)
            }
            formatted_vulns.append(formatted_vuln)

        logger.info(f"成功获取 {len(formatted_vulns)} 个原理漏洞")

        return {
            "status": 0,
            "result": {
                'total_tasks': len(task_ids),
                'task_ids': task_ids,
                'total_vulns': len(formatted_vulns),
                'vulns': formatted_vulns,
                'summary': analysis_result.get('summary', {}),
                'host': host
            }
        }

    except Exception as e:
        logger.error(f"获取所有原理漏洞出错: {str(e)}")
        return {
            "status": 1,
            "result": f"获取所有原理漏洞出错: {str(e)}"
        }


if __name__ == "__main__":
    """本地测试入口"""
    import asyncio

    # 配置日志
    logger.add("nsfocus_vuln_scanner.log", rotation="10 MB", level="INFO")

    async def test_main():
        """测试主函数"""
        logger.info("开始本地测试绿盟漏洞扫描器")

        try:
            # 测试获取所有原理漏洞
            result = await get_all_principle_vulns()

            # 打印结果
            print("\n" + "="*50)
            print("绿盟漏洞扫描器测试结果")
            print("="*50)

            if result["status"] == 0:
                data = result["result"]
                print(f"\n📄 完整JSON结果:\n{json.dumps(result, indent=2, ensure_ascii=False)}")
                print(f"✅ 测试成功!")
                print(f"📊 扫描主机: {data.get('host', 'N/A')}")
                print(f"📋 已完成任务数: {data.get('total_tasks', 0)}")
                print(f"🔍 发现原理漏洞数: {data.get('total_vulns', 0)}")

                # 显示漏洞统计
                summary = data.get('summary', {})
                if summary:
                    print(f"\n📈 漏洞统计:")
                    level_count = summary.get('level_count', {})
                    for level, count in level_count.items():
                        print(f"   {level}: {count} 个")

                    severity_dist = summary.get('severity_distribution', {})
                    if severity_dist:
                        print(f"\n🎯 严重程度分布:")
                        print(f"   高危 (≥7.0): {severity_dist.get('high', 0)} 个")
                        print(f"   中危 (4.0-6.9): {severity_dist.get('medium', 0)} 个")
                        print(f"   低危 (<4.0): {severity_dist.get('low', 0)} 个")

                # 显示前5个漏洞详情
                vulns = data.get('vulns', [])
                if vulns:
                    print(f"\n🔍 前5个原理漏洞详情:")
                    for i, vuln in enumerate(vulns[:5], 1):
                        print(f"\n   {i}. {vuln.get('vuln_name', 'N/A')}")
                        print(f"      主机: {vuln.get('host', 'N/A')}")
                        print(f"      端口: {vuln.get('port', 'N/A')}")
                        print(f"      等级: {vuln.get('vuln_level', 'N/A')}")
                        print(f"      评分: {vuln.get('severity_points', 'N/A')}")

            else:
                print(f"❌ 测试失败: {result['result']}")

            print("\n" + "="*50)

        except Exception as e:
            logger.error(f"本地测试异常: {str(e)}")
            print(f"❌ 测试异常: {str(e)}")

    # 运行测试
    asyncio.run(test_main())
